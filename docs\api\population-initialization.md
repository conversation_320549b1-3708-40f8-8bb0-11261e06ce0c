# 人群初始化 API 文档

## 概述

本文档描述了结直肠癌筛查微观模拟模型的人群初始化功能，包括人群生成器（PopulationGenerator）、配置管理（PopulationConfig）和报告生成（PopulationReporter）的API接口。

## 核心类

### PopulationGenerator

人群生成器类，用于根据配置参数生成具有指定人口统计学特征的个体人群。

#### 构造函数

```python
from src.modules.population import PopulationGenerator

generator = PopulationGenerator(random_seed=42)  # 可选：设置随机种子
```

#### 主要方法

##### generate_population()

生成人群的主要方法。

```python
population = generator.generate_population(
    size=1000,                                    # 人群规模
    age_distribution={                            # 年龄分布配置
        "type": "normal",                         # 分布类型：normal, uniform, custom
        "mean": 60.0,                            # 正态分布均值
        "std": 10.0,                             # 正态分布标准差
        "min_age": 40,                           # 最小年龄
        "max_age": 80                            # 最大年龄
    },
    gender_distribution={                         # 性别分布配置
        "male_ratio": 0.52,                      # 男性比例
        "female_ratio": 0.48                     # 女性比例
    },
    pathway_distribution={                        # 疾病通路分布配置（可选）
        "adenoma_carcinoma_ratio": 0.85,         # 腺瘤-癌变通路比例
        "serrated_adenoma_ratio": 0.15           # 锯齿状腺瘤通路比例
    },
    birth_year_base=2025,                        # 基准年份
    show_progress=True                           # 是否显示进度条
)
```

**参数说明：**

- `size`: 人群规模（1 ≤ size ≤ 10,000,000）
- `age_distribution`: 年龄分布配置字典
  - `type`: 分布类型（"normal", "uniform", "custom"）
  - `mean`, `std`: 正态分布参数（仅当type="normal"时需要）
  - `min_age`, `max_age`: 年龄范围
- `gender_distribution`: 性别分布配置
  - `male_ratio`, `female_ratio`: 男女比例（和必须为1.0）
- `pathway_distribution`: 疾病通路分布（可选）
- `birth_year_base`: 用于计算出生年份的基准年份
- `show_progress`: 是否显示进度条（大于1000个体时）

**返回值：** Population对象

##### generate_batch_populations()

批量生成多个人群。

```python
configs = [
    {
        "size": 500,
        "age_distribution": {"type": "uniform", "min_age": 20, "max_age": 40},
        "gender_distribution": {"male_ratio": 0.5, "female_ratio": 0.5}
    },
    {
        "size": 800,
        "age_distribution": {"type": "normal", "mean": 65, "std": 8, "min_age": 50, "max_age": 80},
        "gender_distribution": {"male_ratio": 0.45, "female_ratio": 0.55}
    }
]

populations = generator.generate_batch_populations(configs, show_progress=True)
```

##### get_last_generation_summary()

获取最后一次生成的摘要信息。

```python
summary = generator.get_last_generation_summary()

print(f"生成时间: {summary.generation_time:.2f}秒")
print(f"总个体数: {summary.total_individuals}")
print(f"平均年龄: {summary.age_stats['mean']:.1f}岁")
```

### PopulationConfig

人群配置管理类，用于加载、验证和管理人群生成配置文件。

#### 构造函数

```python
from src.modules.population import PopulationConfig

config_manager = PopulationConfig()  # 使用默认配置目录
# 或
config_manager = PopulationConfig("custom/config/dir")  # 自定义配置目录
```

#### 主要方法

##### load_config()

加载配置文件（支持YAML和JSON格式）。

```python
config = config_manager.load_config("data/population_configs/china_adult_screening.yaml")

print(f"配置名称: {config.name}")
print(f"人群规模: {config.size}")
print(f"年龄分布类型: {config.age_distribution.type}")
```

##### save_config()

保存配置到文件。

```python
config_manager.save_config(config, "output/my_config.yaml", format="yaml")
# 或
config_manager.save_config(config, "output/my_config.json", format="json")
```

##### create_default_config()

创建默认配置。

```python
default_config = config_manager.create_default_config(
    name="我的人群配置",
    description="自定义人群配置描述"
)

# 修改配置参数
default_config.size = 5000
default_config.age_distribution.mean = 55.0
default_config.gender_distribution.male_ratio = 0.6
```

#### 配置数据结构

##### PopulationConfigData

```python
from src.modules.population.population_config import PopulationConfigData

config = PopulationConfigData(
    name="测试配置",
    description="用于测试的配置",
    size=1000,
    random_seed=42,
    birth_year_base=2025,
    age_distribution=AgeDistributionConfig(...),
    gender_distribution=GenderDistributionConfig(...),
    pathway_distribution=PathwayDistributionConfig(...)
)

# 转换为生成器参数
generation_params = config.to_generation_params()
population = generator.generate_population(**generation_params)
```

### PopulationReporter

人群生成报告器，提供统计报告和可视化功能。

#### 构造函数

```python
from src.modules.population import PopulationReporter

reporter = PopulationReporter()  # 使用默认输出目录
# 或
reporter = PopulationReporter("custom/output/dir")  # 自定义输出目录
```

#### 主要方法

##### generate_summary_report()

生成文本摘要报告。

```python
report_text = reporter.generate_summary_report(
    population=population,
    summary=generation_summary,  # 可选
    save_to_file=True
)

print(report_text)
```

##### generate_comprehensive_report()

生成包含图表的综合报告。

```python
report_info = reporter.generate_comprehensive_report(
    population=population,
    summary=generation_summary,
    include_plots=True  # 需要安装matplotlib
)

print(f"报告文件: {report_info['metadata']['files_generated']}")
```

##### compare_populations()

比较多个人群的统计特征。

```python
comparison_report = reporter.compare_populations(
    populations=[pop1, pop2, pop3],
    population_names=["年轻人群", "中年人群", "老年人群"],
    save_to_file=True
)
```

## 配置文件格式

### YAML格式示例

```yaml
# data/population_configs/example.yaml
population_config:
  name: "示例人群配置"
  description: "用于演示的人群配置"
  
  # 基本参数
  size: 10000
  random_seed: 42
  birth_year_base: 2025
  
  # 年龄分布配置
  age_distribution:
    type: "normal"      # 正态分布
    mean: 62.5          # 平均年龄
    std: 7.2            # 标准差
    min_age: 50         # 最小年龄
    max_age: 75         # 最大年龄
  
  # 性别分布配置
  gender_distribution:
    male_ratio: 0.52    # 男性比例
    female_ratio: 0.48  # 女性比例
  
  # 疾病通路分布配置
  pathway_distribution:
    adenoma_carcinoma_ratio: 0.85   # 腺瘤-癌变通路
    serrated_adenoma_ratio: 0.15    # 锯齿状腺瘤通路
```

### JSON格式示例

```json
{
  "population_config": {
    "name": "JSON示例配置",
    "description": "JSON格式的配置示例",
    "size": 5000,
    "random_seed": 123,
    "age_distribution": {
      "type": "uniform",
      "min_age": 30,
      "max_age": 70
    },
    "gender_distribution": {
      "male_ratio": 0.5,
      "female_ratio": 0.5
    }
  }
}
```

## 使用示例

### 基本使用流程

```python
from src.modules.population import PopulationGenerator, PopulationConfig, PopulationReporter

# 1. 创建生成器
generator = PopulationGenerator(random_seed=42)

# 2. 直接生成人群
population = generator.generate_population(
    size=1000,
    age_distribution={"type": "normal", "mean": 60, "std": 10, "min_age": 40, "max_age": 80},
    gender_distribution={"male_ratio": 0.52, "female_ratio": 0.48},
    show_progress=True
)

# 3. 获取生成摘要
summary = generator.get_last_generation_summary()
print(f"生成了 {summary.total_individuals} 个体，耗时 {summary.generation_time:.2f} 秒")

# 4. 生成报告
reporter = PopulationReporter()
report_info = reporter.generate_comprehensive_report(population, summary)
```

### 使用配置文件

```python
# 1. 加载配置
config_manager = PopulationConfig()
config = config_manager.load_config("data/population_configs/china_adult_screening.yaml")

# 2. 生成人群
generator = PopulationGenerator(random_seed=config.random_seed)
population = generator.generate_population(**config.to_generation_params())

# 3. 验证结果
stats = population.statistics.get_summary()
print(f"生成人群规模: {stats['total_individuals']}")
print(f"平均年龄: {stats['age_statistics']['mean']:.1f}岁")
```

### 批量生成和比较

```python
# 1. 定义多个配置
configs = [
    {
        "size": 1000,
        "age_distribution": {"type": "uniform", "min_age": 20, "max_age": 40},
        "gender_distribution": {"male_ratio": 0.5, "female_ratio": 0.5}
    },
    {
        "size": 1000,
        "age_distribution": {"type": "uniform", "min_age": 60, "max_age": 80},
        "gender_distribution": {"male_ratio": 0.4, "female_ratio": 0.6}
    }
]

# 2. 批量生成
generator = PopulationGenerator(random_seed=42)
populations = generator.generate_batch_populations(configs)

# 3. 比较分析
reporter = PopulationReporter()
comparison = reporter.compare_populations(
    populations=populations,
    population_names=["年轻人群", "老年人群"]
)
print(comparison)
```

## 数据验证

### 参数验证规则

- **人群规模**: 1 ≤ size ≤ 10,000,000
- **年龄范围**: 0 ≤ min_age < max_age ≤ 150
- **性别比例**: male_ratio + female_ratio = 1.0，且都在[0,1]范围内
- **疾病通路比例**: 所有通路比例之和 = 1.0
- **分布参数**: 正态分布的标准差必须 > 0

### 错误处理

```python
from src.utils import ValidationError, ParameterValidationError

try:
    population = generator.generate_population(
        size=0,  # 无效规模
        age_distribution={"type": "normal", "mean": 50, "std": -5},  # 无效标准差
        gender_distribution={"male_ratio": 0.7, "female_ratio": 0.4}  # 比例和不为1
    )
except ParameterValidationError as e:
    print(f"参数验证错误: {e}")
except ValidationError as e:
    print(f"验证错误: {e}")
```

## 性能优化

### 大规模人群生成

```python
# 生成大规模人群时的最佳实践
generator = PopulationGenerator(random_seed=42)

# 对于超过10万个体的人群，建议：
# 1. 使用进度条监控
# 2. 考虑分批生成
# 3. 预分配足够内存

large_population = generator.generate_population(
    size=100000,
    age_distribution={"type": "normal", "mean": 60, "std": 10, "min_age": 30, "max_age": 90},
    gender_distribution={"male_ratio": 0.5, "female_ratio": 0.5},
    show_progress=True  # 显示进度条
)

print(f"生成了 {large_population.get_size():,} 个体")
```

### 内存管理

```python
# 对于内存敏感的应用，可以分批处理
batch_size = 10000
total_size = 100000

all_populations = []
for i in range(0, total_size, batch_size):
    current_size = min(batch_size, total_size - i)
    batch_pop = generator.generate_population(
        size=current_size,
        age_distribution={"type": "uniform", "min_age": 50, "max_age": 75},
        gender_distribution={"male_ratio": 0.5, "female_ratio": 0.5},
        show_progress=False
    )
    all_populations.append(batch_pop)

print(f"分批生成了 {len(all_populations)} 个人群")
```

## 扩展功能

### 自定义年龄分布

```python
# 可以扩展支持更多分布类型
def custom_age_generator(size, params):
    """自定义年龄生成函数"""
    # 实现自定义分布逻辑
    pass

# 在PopulationGenerator中集成自定义分布
```

### 多线程生成

```python
# 对于大规模批量生成，可以考虑并行处理
import concurrent.futures

def generate_single_population(config):
    generator = PopulationGenerator(random_seed=config.get('seed'))
    return generator.generate_population(**config)

# 并行生成多个人群
with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
    futures = [executor.submit(generate_single_population, config) for config in configs]
    populations = [future.result() for future in futures]
```

## 故障排除

### 常见问题

1. **内存不足**: 减少人群规模或使用分批生成
2. **生成时间过长**: 检查配置参数，考虑使用更简单的分布
3. **随机性问题**: 确保设置了随机种子
4. **配置文件错误**: 检查YAML/JSON格式和参数有效性

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 生成小规模测试人群验证配置
test_population = generator.generate_population(
    size=10,  # 小规模测试
    **config.to_generation_params(),
    show_progress=False
)

# 检查生成结果
print(f"测试人群统计: {test_population.statistics.get_summary()}")
```
