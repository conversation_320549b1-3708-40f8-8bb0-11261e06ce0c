#!/usr/bin/env python3
"""
人群结构文件生成演示

演示如何使用PopulationGenerator从Excel/CSV文件生成人群。
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.population import PopulationGenerator, PopulationReporter
from src.core import PathwayType


def create_demo_structure_files():
    """创建演示用的结构文件"""
    print("📁 创建演示用的人群结构文件...")
    
    # 创建一个更复杂的人群结构
    data = []
    
    # 40-75岁人群，每5岁一个年龄组
    for age_group_start in range(40, 76, 5):
        for age in range(age_group_start, min(age_group_start + 5, 76)):
            # 模拟真实的人群分布：中年人群较多
            base_count = 100 if 50 <= age <= 65 else 80
            
            # 男性稍多一些
            male_count = int(base_count * 1.1)
            female_count = base_count
            
            data.append({"age": age, "gender": "male", "count": male_count})
            data.append({"age": age, "gender": "female", "count": female_count})
    
    # 保存为CSV文件
    csv_path = project_root / "data/population_configs/demo_structure.csv"
    df = pd.DataFrame(data)
    df.to_csv(csv_path, index=False)
    print(f"   ✅ 创建CSV文件: {csv_path}")
    
    # 保存为Excel文件
    excel_path = project_root / "data/population_configs/demo_structure.xlsx"
    df.to_excel(excel_path, index=False)
    print(f"   ✅ 创建Excel文件: {excel_path}")
    
    return csv_path, excel_path


def demo_csv_generation():
    """演示从CSV文件生成人群"""
    print("\n🔬 演示1: 从CSV文件生成人群")
    
    csv_path = project_root / "data/population_configs/small_structure_test.csv"
    
    if not csv_path.exists():
        print(f"   ⚠️  文件不存在: {csv_path}")
        return
    
    # 显示文件内容
    print(f"   📄 文件内容预览:")
    df = pd.read_csv(csv_path)
    print(df.head(10).to_string(index=False))
    print(f"   总行数: {len(df)}")
    
    # 生成人群
    generator = PopulationGenerator(random_seed=42)
    
    print(f"\n   🚀 从结构文件生成人群...")
    population = generator.generate_population_from_structure_file(
        csv_path,
        pathway_distribution={
            "adenoma_carcinoma_ratio": 0.8,
            "serrated_adenoma_ratio": 0.2
        },
        show_progress=False
    )
    
    # 显示结果
    print(f"   ✅ 成功生成 {population.get_size()} 个个体")
    
    # 获取生成摘要
    summary = generator.get_last_generation_summary()
    print(f"   ⏱️  生成时间: {summary.generation_time:.3f}秒")
    print(f"   📊 年龄统计: 平均={summary.age_stats['mean']:.1f}岁, 标准差={summary.age_stats['std']:.1f}岁")
    print(f"   👥 性别分布: {summary.gender_distribution}")
    print(f"   🧬 通路分布: {summary.pathway_distribution}")
    
    return population


def demo_excel_generation():
    """演示从Excel文件生成人群"""
    print("\n🔬 演示2: 从Excel文件生成人群")
    
    # 首先创建演示文件
    _, excel_path = create_demo_structure_files()
    
    # 生成人群
    generator = PopulationGenerator(random_seed=123)
    
    print(f"   🚀 从Excel文件生成人群...")
    population = generator.generate_population_from_structure_file(
        excel_path,
        show_progress=False
    )
    
    print(f"   ✅ 成功生成 {population.get_size()} 个个体")
    
    # 显示统计信息
    stats = population.statistics
    age_dist = stats.get_age_distribution()
    gender_dist = stats.get_gender_distribution()
    
    print(f"   📊 年龄分布:")
    for age_group, count in list(age_dist.items())[:5]:
        print(f"      {age_group}: {count} 人")
    
    print(f"   👥 性别分布: {gender_dist}")
    
    return population


def demo_validation_and_error_handling():
    """演示数据验证和错误处理"""
    print("\n🔬 演示3: 数据验证和错误处理")
    
    # 创建包含错误数据的文件
    invalid_data = [
        {"age": -5, "gender": "male", "count": 10},  # 无效年龄
        {"age": 50, "gender": "invalid", "count": 5},  # 无效性别
        {"age": 60, "gender": "female", "count": -2},  # 无效人数
    ]
    
    temp_path = project_root / "temp_invalid.csv"
    df = pd.DataFrame(invalid_data)
    df.to_csv(temp_path, index=False)
    
    generator = PopulationGenerator()
    
    try:
        print(f"   🚀 尝试从无效数据文件生成人群...")
        population = generator.generate_population_from_structure_file(
            temp_path, show_progress=False
        )
    except Exception as e:
        print(f"   ✅ 成功捕获验证错误: {type(e).__name__}")
        print(f"      错误信息: {str(e)}")
    
    # 清理临时文件
    if temp_path.exists():
        temp_path.unlink()


def demo_comparison_with_config_generation():
    """演示与配置文件生成的对比"""
    print("\n🔬 演示4: 与配置文件生成方式对比")
    
    # 方式1: 使用结构文件
    csv_path = project_root / "data/population_configs/small_structure_test.csv"
    generator1 = PopulationGenerator(random_seed=42)
    
    pop1 = generator1.generate_population_from_structure_file(
        csv_path, show_progress=False
    )
    
    # 方式2: 使用配置参数
    generator2 = PopulationGenerator(random_seed=42)
    pop2 = generator2.generate_population(
        size=74,  # 与结构文件总数相同
        age_distribution={"type": "uniform", "min_age": 45, "max_age": 70},
        gender_distribution={"male_ratio": 0.5, "female_ratio": 0.5},
        show_progress=False
    )
    
    print(f"   📊 结构文件生成: {pop1.get_size()} 个体")
    print(f"   📊 配置参数生成: {pop2.get_size()} 个体")
    
    # 比较年龄分布
    stats1 = pop1.statistics.get_age_statistics()
    stats2 = pop2.statistics.get_age_statistics()
    
    print(f"   📈 年龄统计对比:")
    print(f"      结构文件: 平均={stats1['mean']:.1f}岁, 标准差={stats1['std']:.1f}岁")
    print(f"      配置参数: 平均={stats2['mean']:.1f}岁, 标准差={stats2['std']:.1f}岁")


def main():
    """主函数"""
    print("🚀 人群结构文件生成功能演示")
    print("=" * 50)
    
    try:
        # 演示1: CSV文件生成
        demo_csv_generation()
        
        # 演示2: Excel文件生成
        demo_excel_generation()
        
        # 演示3: 数据验证
        demo_validation_and_error_handling()
        
        # 演示4: 对比分析
        demo_comparison_with_config_generation()
        
        print("\n✅ 所有演示完成!")
        print("\n📝 总结:")
        print("   - 支持从CSV和Excel文件生成人群")
        print("   - 文件格式: age, gender, count 三列")
        print("   - 自动验证数据有效性")
        print("   - 支持疾病通路分布配置")
        print("   - 提供详细的生成摘要")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
