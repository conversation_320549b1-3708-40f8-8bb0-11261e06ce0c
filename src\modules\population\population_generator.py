"""
人群生成器模块

实现PopulationGenerator类，用于根据配置参数生成具有指定
人口统计学特征的个体人群。
"""

import uuid
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass
import numpy as np
from tqdm import tqdm

from ...core import Individual, Population, Gender, DiseaseState, PathwayType
from ...utils import (
    validate_age, validate_positive_number, validate_probability,
    ValidationError, ParameterValidationError
)


@dataclass
class GenerationSummary:
    """人群生成摘要"""
    total_individuals: int
    generation_time: float
    age_stats: Dict[str, float]
    gender_distribution: Dict[str, int]
    pathway_distribution: Dict[str, int]
    config_used: Dict[str, Any]


class PopulationGenerator:
    """
    人群生成器类
    
    根据配置参数生成具有指定人口统计学特征的个体人群，
    支持多种年龄分布、性别比例和疾病通路分配。
    """
    
    def __init__(self, random_seed: Optional[int] = None):
        """
        初始化人群生成器
        
        Args:
            random_seed: 随机种子，用于确保结果可重现
        """
        self.random_seed = random_seed
        if random_seed is not None:
            np.random.seed(random_seed)
        
        self._last_generation_summary: Optional[GenerationSummary] = None
    
    def generate_population(
        self,
        size: int,
        age_distribution: Dict[str, Any],
        gender_distribution: Dict[str, float],
        pathway_distribution: Optional[Dict[str, float]] = None,
        birth_year_base: int = 2025,
        show_progress: bool = True
    ) -> Population:
        """
        生成人群
        
        Args:
            size: 人群规模
            age_distribution: 年龄分布配置
            gender_distribution: 性别分布配置
            pathway_distribution: 疾病通路分布配置
            birth_year_base: 基准年份，用于计算出生年份
            show_progress: 是否显示进度条
            
        Returns:
            生成的人群对象
            
        Raises:
            ValidationError: 参数验证失败时
        """
        import time
        start_time = time.time()
        
        # 验证参数
        self._validate_generation_parameters(
            size, age_distribution, gender_distribution, pathway_distribution
        )
        
        # 生成年龄分布
        ages = self._generate_age_distribution(size, age_distribution)
        
        # 分配性别
        genders = self._assign_genders(size, gender_distribution)
        
        # 分配疾病通路类型
        pathway_types = self._assign_pathway_types(size, pathway_distribution)
        
        # 创建个体列表
        individuals = []
        
        # 使用进度条显示生成进度
        iterator = range(size)
        if show_progress and size > 1000:
            iterator = tqdm(iterator, desc="生成个体", unit="个")
        
        for i in iterator:
            birth_year = birth_year_base - int(ages[i])
            individual = Individual(
                birth_year=birth_year,
                gender=genders[i],
                individual_id=str(uuid.uuid4()),
                pathway_type=pathway_types[i] if pathway_types else None
            )
            individuals.append(individual)
        
        # 创建人群对象
        population = Population(individuals)
        
        # 生成摘要
        generation_time = time.time() - start_time
        self._last_generation_summary = self._create_generation_summary(
            population, generation_time, {
                "size": size,
                "age_distribution": age_distribution,
                "gender_distribution": gender_distribution,
                "pathway_distribution": pathway_distribution,
                "birth_year_base": birth_year_base
            }
        )
        
        return population
    
    def _validate_generation_parameters(
        self,
        size: int,
        age_distribution: Dict[str, Any],
        gender_distribution: Dict[str, float],
        pathway_distribution: Optional[Dict[str, float]]
    ) -> None:
        """验证生成参数"""
        # 验证人群规模
        if not isinstance(size, int) or size <= 0:
            raise ParameterValidationError(
                "人群规模必须是正整数",
                "size",
                size
            )
        
        if size > 10_000_000:
            raise ParameterValidationError(
                "人群规模不能超过1000万",
                "size", 
                size
            )
        
        # 验证年龄分布参数
        self._validate_age_distribution(age_distribution)
        
        # 验证性别分布参数
        self._validate_gender_distribution(gender_distribution)
        
        # 验证疾病通路分布参数
        if pathway_distribution:
            self._validate_pathway_distribution(pathway_distribution)
    
    def _validate_age_distribution(self, age_dist: Dict[str, Any]) -> None:
        """验证年龄分布参数"""
        if not isinstance(age_dist, dict):
            raise ParameterValidationError(
                "年龄分布配置必须是字典类型",
                "age_distribution",
                age_dist
            )
        
        dist_type = age_dist.get("type", "normal")
        if dist_type not in ["normal", "uniform", "custom"]:
            raise ParameterValidationError(
                "年龄分布类型必须是 'normal', 'uniform' 或 'custom'",
                "age_distribution.type",
                dist_type
            )
        
        # 验证年龄范围
        min_age = age_dist.get("min_age", 18)
        max_age = age_dist.get("max_age", 100)
        
        validate_age(min_age, "min_age")
        validate_age(max_age, "max_age")
        
        if min_age > max_age:
            raise ParameterValidationError(
                "最小年龄必须小于或等于最大年龄",
                "age_distribution",
                {"min_age": min_age, "max_age": max_age}
            )
        
        # 根据分布类型验证特定参数
        if dist_type == "normal":
            mean = age_dist.get("mean")
            std = age_dist.get("std")
            
            if mean is None or std is None:
                raise ParameterValidationError(
                    "正态分布需要指定 'mean' 和 'std' 参数",
                    "age_distribution",
                    age_dist
                )
            
            validate_age(mean, "mean")
            validate_positive_number(std, "std")
            
            if not (min_age <= mean <= max_age):
                raise ParameterValidationError(
                    "年龄均值必须在最小年龄和最大年龄之间",
                    "age_distribution.mean",
                    mean
                )
    
    def _validate_gender_distribution(self, gender_dist: Dict[str, float]) -> None:
        """验证性别分布参数"""
        if not isinstance(gender_dist, dict):
            raise ParameterValidationError(
                "性别分布配置必须是字典类型",
                "gender_distribution",
                gender_dist
            )
        
        male_ratio = gender_dist.get("male_ratio")
        female_ratio = gender_dist.get("female_ratio")
        
        if male_ratio is None or female_ratio is None:
            raise ParameterValidationError(
                "性别分布必须包含 'male_ratio' 和 'female_ratio'",
                "gender_distribution",
                gender_dist
            )
        
        validate_probability(male_ratio, "male_ratio")
        validate_probability(female_ratio, "female_ratio")
        
        total_ratio = male_ratio + female_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ParameterValidationError(
                "男女比例之和必须等于1.0",
                "gender_distribution",
                {"male_ratio": male_ratio, "female_ratio": female_ratio, "sum": total_ratio}
            )
    
    def _validate_pathway_distribution(self, pathway_dist: Dict[str, float]) -> None:
        """验证疾病通路分布参数"""
        if not isinstance(pathway_dist, dict):
            raise ParameterValidationError(
                "疾病通路分布配置必须是字典类型",
                "pathway_distribution",
                pathway_dist
            )
        
        adenoma_ratio = pathway_dist.get("adenoma_carcinoma_ratio", 0)
        serrated_ratio = pathway_dist.get("serrated_adenoma_ratio", 0)
        
        validate_probability(adenoma_ratio, "adenoma_carcinoma_ratio")
        validate_probability(serrated_ratio, "serrated_adenoma_ratio")
        
        total_ratio = adenoma_ratio + serrated_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ParameterValidationError(
                "疾病通路比例之和必须等于1.0",
                "pathway_distribution",
                {"adenoma_carcinoma_ratio": adenoma_ratio, "serrated_adenoma_ratio": serrated_ratio, "sum": total_ratio}
            )
    
    def _generate_age_distribution(
        self, 
        size: int, 
        age_dist: Dict[str, Any]
    ) -> np.ndarray:
        """生成年龄分布"""
        dist_type = age_dist.get("type", "normal")
        min_age = age_dist.get("min_age", 18)
        max_age = age_dist.get("max_age", 100)
        
        if dist_type == "normal":
            mean = age_dist["mean"]
            std = age_dist["std"]
            
            # 生成正态分布年龄，并截断到指定范围
            ages = np.random.normal(mean, std, size)
            ages = np.clip(ages, min_age, max_age)
            
        elif dist_type == "uniform":
            # 生成均匀分布年龄
            ages = np.random.uniform(min_age, max_age, size)
            
        elif dist_type == "custom":
            # 自定义分布（这里可以扩展支持更多分布类型）
            ages = np.random.uniform(min_age, max_age, size)
            
        return ages
    
    def _assign_genders(
        self, 
        size: int, 
        gender_dist: Dict[str, float]
    ) -> List[Gender]:
        """分配性别"""
        male_ratio = gender_dist["male_ratio"]
        
        # 生成随机数并根据比例分配性别
        random_values = np.random.random(size)
        genders = [
            Gender.MALE if rv < male_ratio else Gender.FEMALE
            for rv in random_values
        ]
        
        return genders
    
    def _assign_pathway_types(
        self, 
        size: int, 
        pathway_dist: Optional[Dict[str, float]]
    ) -> Optional[List[Optional[PathwayType]]]:
        """分配疾病通路类型"""
        if not pathway_dist:
            return None
        
        adenoma_ratio = pathway_dist.get("adenoma_carcinoma_ratio", 0.85)
        
        # 生成随机数并根据比例分配通路类型
        random_values = np.random.random(size)
        pathway_types = [
            PathwayType.ADENOMA_CARCINOMA if rv < adenoma_ratio 
            else PathwayType.SERRATED_ADENOMA
            for rv in random_values
        ]
        
        return pathway_types
    
    def _create_generation_summary(
        self,
        population: Population,
        generation_time: float,
        config: Dict[str, Any]
    ) -> GenerationSummary:
        """创建生成摘要"""
        stats = population.statistics

        # 计算年龄统计
        ages = [ind.get_current_age() for ind in population]
        if ages:
            age_stats = {
                "mean": float(np.mean(ages)),
                "median": float(np.median(ages)),
                "std": float(np.std(ages)),
                "min": float(np.min(ages)),
                "max": float(np.max(ages))
            }
        else:
            age_stats = {
                "mean": 0.0,
                "median": 0.0,
                "std": 0.0,
                "min": 0.0,
                "max": 0.0
            }
        
        return GenerationSummary(
            total_individuals=population.get_size(),
            generation_time=generation_time,
            age_stats=age_stats,
            gender_distribution=stats.get_gender_distribution(),
            pathway_distribution=stats.get_pathway_distribution(),
            config_used=config
        )
    
    def get_last_generation_summary(self) -> Optional[GenerationSummary]:
        """获取最后一次生成的摘要"""
        return self._last_generation_summary
    
    def generate_batch_populations(
        self,
        configs: List[Dict[str, Any]],
        show_progress: bool = True
    ) -> List[Population]:
        """
        批量生成多个人群
        
        Args:
            configs: 配置列表，每个配置对应一个人群
            show_progress: 是否显示进度条
            
        Returns:
            生成的人群列表
        """
        populations = []
        
        iterator = configs
        if show_progress:
            iterator = tqdm(configs, desc="批量生成人群", unit="个人群")
        
        for config in iterator:
            # 确保不传递重复的show_progress参数
            config_copy = config.copy()
            config_copy.pop('show_progress', None)
            population = self.generate_population(**config_copy, show_progress=False)
            populations.append(population)
        
        return populations
