"""
人群生成器模块

实现PopulationGenerator类，用于根据配置参数生成具有指定
人口统计学特征的个体人群。
"""

import uuid
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass
from pathlib import Path
import numpy as np
import pandas as pd
from tqdm import tqdm

from ...core import Individual, Population, Gender, DiseaseState, PathwayType
from ...utils import (
    validate_age, validate_positive_number, validate_probability,
    ValidationError, ParameterValidationError
)


@dataclass
class GenerationSummary:
    """人群生成摘要"""
    total_individuals: int
    generation_time: float
    age_stats: Dict[str, float]
    gender_distribution: Dict[str, int]
    pathway_distribution: Dict[str, int]
    config_used: Dict[str, Any]


@dataclass
class PopulationStructureRow:
    """人群结构行数据"""
    age: Union[int, float]
    gender: str
    count: int

    def __post_init__(self):
        """验证数据"""
        if self.age < 0 or self.age > 150:
            raise ValueError(f"年龄必须在0-150之间，得到: {self.age}")

        if self.gender.lower() not in ['male', 'female', 'm', 'f', '男', '女']:
            raise ValueError(f"性别值无效: {self.gender}")

        if self.count <= 0:
            raise ValueError(f"人数必须大于0，得到: {self.count}")

        # 标准化性别值
        gender_mapping = {
            'm': 'male', 'male': 'male', '男': 'male',
            'f': 'female', 'female': 'female', '女': 'female'
        }
        self.gender = gender_mapping.get(self.gender.lower(), self.gender.lower())


class PopulationGenerator:
    """
    人群生成器类
    
    根据配置参数生成具有指定人口统计学特征的个体人群，
    支持多种年龄分布、性别比例和疾病通路分配。
    """
    
    def __init__(self, random_seed: Optional[int] = None):
        """
        初始化人群生成器
        
        Args:
            random_seed: 随机种子，用于确保结果可重现
        """
        self.random_seed = random_seed
        if random_seed is not None:
            np.random.seed(random_seed)
        
        self._last_generation_summary: Optional[GenerationSummary] = None
    
    def generate_population(
        self,
        size: int,
        age_distribution: Dict[str, Any],
        gender_distribution: Dict[str, float],
        pathway_distribution: Optional[Dict[str, float]] = None,
        birth_year_base: int = 2025,
        show_progress: bool = True
    ) -> Population:
        """
        生成人群
        
        Args:
            size: 人群规模
            age_distribution: 年龄分布配置
            gender_distribution: 性别分布配置
            pathway_distribution: 疾病通路分布配置
            birth_year_base: 基准年份，用于计算出生年份
            show_progress: 是否显示进度条
            
        Returns:
            生成的人群对象
            
        Raises:
            ValidationError: 参数验证失败时
        """
        import time
        start_time = time.time()
        
        # 验证参数
        self._validate_generation_parameters(
            size, age_distribution, gender_distribution, pathway_distribution
        )
        
        # 生成年龄分布
        ages = self._generate_age_distribution(size, age_distribution)
        
        # 分配性别
        genders = self._assign_genders(size, gender_distribution)
        
        # 分配疾病通路类型
        pathway_types = self._assign_pathway_types(size, pathway_distribution)
        
        # 创建个体列表
        individuals = []
        
        # 使用进度条显示生成进度
        iterator = range(size)
        if show_progress and size > 1000:
            iterator = tqdm(iterator, desc="生成个体", unit="个")
        
        for i in iterator:
            birth_year = birth_year_base - int(ages[i])
            individual = Individual(
                birth_year=birth_year,
                gender=genders[i],
                individual_id=str(uuid.uuid4()),
                pathway_type=pathway_types[i] if pathway_types else None
            )
            individuals.append(individual)
        
        # 创建人群对象
        population = Population(individuals)
        
        # 生成摘要
        generation_time = time.time() - start_time
        self._last_generation_summary = self._create_generation_summary(
            population, generation_time, {
                "size": size,
                "age_distribution": age_distribution,
                "gender_distribution": gender_distribution,
                "pathway_distribution": pathway_distribution,
                "birth_year_base": birth_year_base
            }
        )
        
        return population
    
    def _validate_generation_parameters(
        self,
        size: int,
        age_distribution: Dict[str, Any],
        gender_distribution: Dict[str, float],
        pathway_distribution: Optional[Dict[str, float]]
    ) -> None:
        """验证生成参数"""
        # 验证人群规模
        if not isinstance(size, int) or size <= 0:
            raise ParameterValidationError(
                "人群规模必须是正整数",
                "size",
                size
            )
        
        if size > 10_000_000:
            raise ParameterValidationError(
                "人群规模不能超过1000万",
                "size", 
                size
            )
        
        # 验证年龄分布参数
        self._validate_age_distribution(age_distribution)
        
        # 验证性别分布参数
        self._validate_gender_distribution(gender_distribution)
        
        # 验证疾病通路分布参数
        if pathway_distribution:
            self._validate_pathway_distribution(pathway_distribution)
    
    def _validate_age_distribution(self, age_dist: Dict[str, Any]) -> None:
        """验证年龄分布参数"""
        if not isinstance(age_dist, dict):
            raise ParameterValidationError(
                "年龄分布配置必须是字典类型",
                "age_distribution",
                age_dist
            )
        
        dist_type = age_dist.get("type", "normal")
        if dist_type not in ["normal", "uniform", "custom"]:
            raise ParameterValidationError(
                "年龄分布类型必须是 'normal', 'uniform' 或 'custom'",
                "age_distribution.type",
                dist_type
            )
        
        # 验证年龄范围
        min_age = age_dist.get("min_age", 18)
        max_age = age_dist.get("max_age", 100)
        
        validate_age(min_age, "min_age")
        validate_age(max_age, "max_age")
        
        if min_age > max_age:
            raise ParameterValidationError(
                "最小年龄必须小于或等于最大年龄",
                "age_distribution",
                {"min_age": min_age, "max_age": max_age}
            )
        
        # 根据分布类型验证特定参数
        if dist_type == "normal":
            mean = age_dist.get("mean")
            std = age_dist.get("std")
            
            if mean is None or std is None:
                raise ParameterValidationError(
                    "正态分布需要指定 'mean' 和 'std' 参数",
                    "age_distribution",
                    age_dist
                )
            
            validate_age(mean, "mean")
            validate_positive_number(std, "std")
            
            if not (min_age <= mean <= max_age):
                raise ParameterValidationError(
                    "年龄均值必须在最小年龄和最大年龄之间",
                    "age_distribution.mean",
                    mean
                )
    
    def _validate_gender_distribution(self, gender_dist: Dict[str, float]) -> None:
        """验证性别分布参数"""
        if not isinstance(gender_dist, dict):
            raise ParameterValidationError(
                "性别分布配置必须是字典类型",
                "gender_distribution",
                gender_dist
            )
        
        male_ratio = gender_dist.get("male_ratio")
        female_ratio = gender_dist.get("female_ratio")
        
        if male_ratio is None or female_ratio is None:
            raise ParameterValidationError(
                "性别分布必须包含 'male_ratio' 和 'female_ratio'",
                "gender_distribution",
                gender_dist
            )
        
        validate_probability(male_ratio, "male_ratio")
        validate_probability(female_ratio, "female_ratio")
        
        total_ratio = male_ratio + female_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ParameterValidationError(
                "男女比例之和必须等于1.0",
                "gender_distribution",
                {"male_ratio": male_ratio, "female_ratio": female_ratio, "sum": total_ratio}
            )
    
    def _validate_pathway_distribution(self, pathway_dist: Dict[str, float]) -> None:
        """验证疾病通路分布参数"""
        if not isinstance(pathway_dist, dict):
            raise ParameterValidationError(
                "疾病通路分布配置必须是字典类型",
                "pathway_distribution",
                pathway_dist
            )
        
        adenoma_ratio = pathway_dist.get("adenoma_carcinoma_ratio", 0)
        serrated_ratio = pathway_dist.get("serrated_adenoma_ratio", 0)
        
        validate_probability(adenoma_ratio, "adenoma_carcinoma_ratio")
        validate_probability(serrated_ratio, "serrated_adenoma_ratio")
        
        total_ratio = adenoma_ratio + serrated_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ParameterValidationError(
                "疾病通路比例之和必须等于1.0",
                "pathway_distribution",
                {"adenoma_carcinoma_ratio": adenoma_ratio, "serrated_adenoma_ratio": serrated_ratio, "sum": total_ratio}
            )
    
    def _generate_age_distribution(
        self, 
        size: int, 
        age_dist: Dict[str, Any]
    ) -> np.ndarray:
        """生成年龄分布"""
        dist_type = age_dist.get("type", "normal")
        min_age = age_dist.get("min_age", 18)
        max_age = age_dist.get("max_age", 100)
        
        if dist_type == "normal":
            mean = age_dist["mean"]
            std = age_dist["std"]
            
            # 生成正态分布年龄，并截断到指定范围
            ages = np.random.normal(mean, std, size)
            ages = np.clip(ages, min_age, max_age)
            
        elif dist_type == "uniform":
            # 生成均匀分布年龄
            ages = np.random.uniform(min_age, max_age, size)
            
        elif dist_type == "custom":
            # 自定义分布（这里可以扩展支持更多分布类型）
            ages = np.random.uniform(min_age, max_age, size)
            
        return ages
    
    def _assign_genders(
        self, 
        size: int, 
        gender_dist: Dict[str, float]
    ) -> List[Gender]:
        """分配性别"""
        male_ratio = gender_dist["male_ratio"]
        
        # 生成随机数并根据比例分配性别
        random_values = np.random.random(size)
        genders = [
            Gender.MALE if rv < male_ratio else Gender.FEMALE
            for rv in random_values
        ]
        
        return genders
    
    def _assign_pathway_types(
        self, 
        size: int, 
        pathway_dist: Optional[Dict[str, float]]
    ) -> Optional[List[Optional[PathwayType]]]:
        """分配疾病通路类型"""
        if not pathway_dist:
            return None
        
        adenoma_ratio = pathway_dist.get("adenoma_carcinoma_ratio", 0.85)
        
        # 生成随机数并根据比例分配通路类型
        random_values = np.random.random(size)
        pathway_types = [
            PathwayType.ADENOMA_CARCINOMA if rv < adenoma_ratio 
            else PathwayType.SERRATED_ADENOMA
            for rv in random_values
        ]
        
        return pathway_types
    
    def _create_generation_summary(
        self,
        population: Population,
        generation_time: float,
        config: Dict[str, Any]
    ) -> GenerationSummary:
        """创建生成摘要"""
        stats = population.statistics

        # 计算年龄统计
        ages = [ind.get_current_age() for ind in population]
        if ages:
            age_stats = {
                "mean": float(np.mean(ages)),
                "median": float(np.median(ages)),
                "std": float(np.std(ages)),
                "min": float(np.min(ages)),
                "max": float(np.max(ages))
            }
        else:
            age_stats = {
                "mean": 0.0,
                "median": 0.0,
                "std": 0.0,
                "min": 0.0,
                "max": 0.0
            }
        
        return GenerationSummary(
            total_individuals=population.get_size(),
            generation_time=generation_time,
            age_stats=age_stats,
            gender_distribution=stats.get_gender_distribution(),
            pathway_distribution=stats.get_pathway_distribution(),
            config_used=config
        )
    
    def get_last_generation_summary(self) -> Optional[GenerationSummary]:
        """获取最后一次生成的摘要"""
        return self._last_generation_summary
    
    def generate_batch_populations(
        self,
        configs: List[Dict[str, Any]],
        show_progress: bool = True
    ) -> List[Population]:
        """
        批量生成多个人群
        
        Args:
            configs: 配置列表，每个配置对应一个人群
            show_progress: 是否显示进度条
            
        Returns:
            生成的人群列表
        """
        populations = []
        
        iterator = configs
        if show_progress:
            iterator = tqdm(configs, desc="批量生成人群", unit="个人群")
        
        for config in iterator:
            # 确保不传递重复的show_progress参数
            config_copy = config.copy()
            config_copy.pop('show_progress', None)
            population = self.generate_population(**config_copy, show_progress=False)
            populations.append(population)
        
        return populations

    def generate_population_from_structure_file(
        self,
        file_path: Union[str, Path],
        pathway_distribution: Optional[Dict[str, float]] = None,
        birth_year_base: int = 2025,
        show_progress: bool = True
    ) -> Population:
        """
        从结构文件生成人群

        文件格式要求：
        - CSV/Excel文件，包含三列：age（年龄）、gender（性别）、count（人数）
        - 年龄：数值，0-150之间
        - 性别：male/female、m/f、男/女
        - 人数：正整数

        Args:
            file_path: 结构文件路径（支持.csv, .xlsx, .xls格式）
            pathway_distribution: 疾病通路分布配置（可选）
            birth_year_base: 基准年份，用于计算出生年份
            show_progress: 是否显示进度条

        Returns:
            生成的人群对象

        Raises:
            ValidationError: 文件格式错误或数据验证失败时
            FileNotFoundError: 文件不存在时
        """
        import time
        start_time = time.time()

        # 加载和验证结构数据
        structure_data = self._load_population_structure_file(file_path)

        # 验证疾病通路分布参数
        if pathway_distribution:
            self._validate_pathway_distribution(pathway_distribution)

        # 根据结构数据生成个体
        individuals = self._generate_individuals_from_structure(
            structure_data, pathway_distribution, birth_year_base, show_progress
        )

        # 创建人群对象
        population = Population(individuals)

        # 生成摘要
        generation_time = time.time() - start_time
        total_size = sum(row.count for row in structure_data)

        self._last_generation_summary = self._create_generation_summary(
            population, generation_time, {
                "source_file": str(file_path),
                "total_structure_rows": len(structure_data),
                "total_individuals": total_size,
                "pathway_distribution": pathway_distribution,
                "birth_year_base": birth_year_base
            }
        )

        return population

    def _load_population_structure_file(self, file_path: Union[str, Path]) -> List[PopulationStructureRow]:
        """
        加载人群结构文件

        Args:
            file_path: 文件路径

        Returns:
            人群结构行数据列表

        Raises:
            ValidationError: 文件格式错误或数据验证失败时
            FileNotFoundError: 文件不存在时
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"人群结构文件不存在: {file_path}")

        # 根据文件扩展名选择加载方法
        try:
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                raise ValidationError(
                    f"不支持的文件格式: {file_path.suffix}。支持的格式: .csv, .xlsx, .xls",
                    "file_format",
                    str(file_path)
                )
        except Exception as e:
            raise ValidationError(
                f"文件读取失败: {e}",
                "file_reading",
                str(file_path)
            )

        # 验证必需的列
        required_columns = ['age', 'gender', 'count']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise ValidationError(
                f"文件缺少必需的列: {missing_columns}。必需列: {required_columns}",
                "missing_columns",
                list(df.columns)
            )

        # 转换为结构行数据
        structure_data = []
        for index, row in df.iterrows():
            try:
                structure_row = PopulationStructureRow(
                    age=row['age'],
                    gender=str(row['gender']),
                    count=int(row['count'])
                )
                structure_data.append(structure_row)
            except (ValueError, TypeError) as e:
                raise ValidationError(
                    f"第{index + 1}行数据无效: {e}",
                    f"row_{index + 1}",
                    dict(row)
                )

        if not structure_data:
            raise ValidationError(
                "文件中没有有效的人群结构数据",
                "empty_data",
                str(file_path)
            )

        return structure_data

    def _generate_individuals_from_structure(
        self,
        structure_data: List[PopulationStructureRow],
        pathway_distribution: Optional[Dict[str, float]],
        birth_year_base: int,
        show_progress: bool
    ) -> List[Individual]:
        """
        根据结构数据生成个体列表

        Args:
            structure_data: 人群结构数据
            pathway_distribution: 疾病通路分布配置
            birth_year_base: 基准年份
            show_progress: 是否显示进度条

        Returns:
            个体列表
        """
        individuals = []
        total_count = sum(row.count for row in structure_data)

        # 预先分配疾病通路类型
        pathway_types = None
        if pathway_distribution:
            pathway_types = self._assign_pathway_types(total_count, pathway_distribution)

        # 使用进度条
        iterator = structure_data
        if show_progress and total_count > 1000:
            iterator = tqdm(structure_data, desc="根据结构生成个体", unit="组")

        pathway_index = 0

        for structure_row in iterator:
            # 转换性别
            gender = Gender.MALE if structure_row.gender == 'male' else Gender.FEMALE

            # 为这个年龄性别组生成指定数量的个体
            for _ in range(structure_row.count):
                birth_year = birth_year_base - int(structure_row.age)

                # 分配疾病通路类型
                pathway_type = None
                if pathway_types and pathway_index < len(pathway_types):
                    pathway_type = pathway_types[pathway_index]
                    pathway_index += 1

                individual = Individual(
                    birth_year=birth_year,
                    gender=gender,
                    individual_id=str(uuid.uuid4()),
                    pathway_type=pathway_type
                )
                individuals.append(individual)

        return individuals
