"""
人群结构文件生成功能测试

测试PopulationGenerator从Excel/CSV文件生成人群的功能。
"""

import pytest
import pandas as pd
from pathlib import Path
from unittest.mock import patch
import tempfile
import os

from src.modules.population import PopulationGenerator
from src.modules.population.population_generator import PopulationStructureRow
from src.core import Gender, PathwayType
from src.utils import ValidationError


class TestPopulationStructureRow:
    """测试PopulationStructureRow数据类"""
    
    def test_valid_structure_row(self):
        """测试有效的结构行数据"""
        row = PopulationStructureRow(age=50, gender="male", count=100)
        assert row.age == 50
        assert row.gender == "male"
        assert row.count == 100
    
    def test_gender_normalization(self):
        """测试性别值标准化"""
        test_cases = [
            ("male", "male"),
            ("Male", "male"),
            ("m", "male"),
            ("M", "male"),
            ("男", "male"),
            ("female", "female"),
            ("Female", "female"),
            ("f", "female"),
            ("F", "female"),
            ("女", "female")
        ]
        
        for input_gender, expected in test_cases:
            row = PopulationStructureRow(age=50, gender=input_gender, count=10)
            assert row.gender == expected
    
    def test_invalid_age(self):
        """测试无效年龄"""
        with pytest.raises(ValueError, match="年龄必须在0-150之间"):
            PopulationStructureRow(age=-1, gender="male", count=10)
        
        with pytest.raises(ValueError, match="年龄必须在0-150之间"):
            PopulationStructureRow(age=200, gender="male", count=10)
    
    def test_invalid_gender(self):
        """测试无效性别"""
        with pytest.raises(ValueError, match="性别值无效"):
            PopulationStructureRow(age=50, gender="invalid", count=10)
    
    def test_invalid_count(self):
        """测试无效人数"""
        with pytest.raises(ValueError, match="人数必须大于0"):
            PopulationStructureRow(age=50, gender="male", count=0)
        
        with pytest.raises(ValueError, match="人数必须大于0"):
            PopulationStructureRow(age=50, gender="male", count=-5)


class TestPopulationGeneratorStructureFile:
    """测试PopulationGenerator结构文件功能"""
    
    def setup_method(self):
        """测试设置"""
        self.generator = PopulationGenerator(random_seed=42)
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """测试清理"""
        # 清理临时文件
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def create_test_csv(self, filename: str, data: list) -> Path:
        """创建测试CSV文件"""
        file_path = self.temp_dir / filename
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False)
        return file_path
    
    def create_test_excel(self, filename: str, data: list) -> Path:
        """创建测试Excel文件"""
        file_path = self.temp_dir / filename
        df = pd.DataFrame(data)
        df.to_excel(file_path, index=False)
        return file_path
    
    def test_load_valid_csv_structure(self):
        """测试加载有效的CSV结构文件"""
        data = [
            {"age": 50, "gender": "male", "count": 10},
            {"age": 50, "gender": "female", "count": 8},
            {"age": 60, "gender": "male", "count": 12},
            {"age": 60, "gender": "female", "count": 15}
        ]
        
        csv_file = self.create_test_csv("test_structure.csv", data)
        structure_data = self.generator._load_population_structure_file(csv_file)
        
        assert len(structure_data) == 4
        assert structure_data[0].age == 50
        assert structure_data[0].gender == "male"
        assert structure_data[0].count == 10
    
    def test_load_valid_excel_structure(self):
        """测试加载有效的Excel结构文件"""
        data = [
            {"age": 45, "gender": "female", "count": 20},
            {"age": 55, "gender": "male", "count": 25}
        ]
        
        excel_file = self.create_test_excel("test_structure.xlsx", data)
        structure_data = self.generator._load_population_structure_file(excel_file)
        
        assert len(structure_data) == 2
        assert structure_data[0].age == 45
        assert structure_data[0].gender == "female"
        assert structure_data[0].count == 20
    
    def test_file_not_found(self):
        """测试文件不存在"""
        non_existent_file = self.temp_dir / "non_existent.csv"
        
        with pytest.raises(FileNotFoundError, match="人群结构文件不存在"):
            self.generator._load_population_structure_file(non_existent_file)
    
    def test_unsupported_file_format(self):
        """测试不支持的文件格式"""
        txt_file = self.temp_dir / "test.txt"
        txt_file.write_text("some content")
        
        with pytest.raises(ValidationError, match="不支持的文件格式"):
            self.generator._load_population_structure_file(txt_file)
    
    def test_missing_required_columns(self):
        """测试缺少必需列"""
        data = [
            {"age": 50, "gender": "male"},  # 缺少count列
            {"age": 60, "gender": "female"}
        ]
        
        csv_file = self.create_test_csv("missing_columns.csv", data)
        
        with pytest.raises(ValidationError, match="文件缺少必需的列"):
            self.generator._load_population_structure_file(csv_file)
    
    def test_invalid_data_in_file(self):
        """测试文件中的无效数据"""
        data = [
            {"age": -5, "gender": "male", "count": 10},  # 无效年龄
        ]
        
        csv_file = self.create_test_csv("invalid_data.csv", data)
        
        with pytest.raises(ValidationError, match="数据无效"):
            self.generator._load_population_structure_file(csv_file)
    
    def test_generate_population_from_structure_file(self):
        """测试从结构文件生成人群"""
        data = [
            {"age": 50, "gender": "male", "count": 5},
            {"age": 50, "gender": "female", "count": 3},
            {"age": 60, "gender": "male", "count": 4},
            {"age": 60, "gender": "female", "count": 6}
        ]
        
        csv_file = self.create_test_csv("population_structure.csv", data)
        
        population = self.generator.generate_population_from_structure_file(
            csv_file, show_progress=False
        )
        
        # 验证生成的人群
        assert population.get_size() == 18  # 5+3+4+6
        
        # 验证年龄分布
        age_50_count = sum(1 for ind in population.individuals.values() if ind.get_current_age() == 50)
        age_60_count = sum(1 for ind in population.individuals.values() if ind.get_current_age() == 60)
        
        assert age_50_count == 8  # 5+3
        assert age_60_count == 10  # 4+6
        
        # 验证性别分布
        male_count = sum(1 for ind in population.individuals.values() if ind.gender == Gender.MALE)
        female_count = sum(1 for ind in population.individuals.values() if ind.gender == Gender.FEMALE)
        
        assert male_count == 9  # 5+4
        assert female_count == 9  # 3+6
    
    def test_generate_with_pathway_distribution(self):
        """测试带疾病通路分布的生成"""
        data = [
            {"age": 55, "gender": "male", "count": 10},
            {"age": 55, "gender": "female", "count": 10}
        ]
        
        csv_file = self.create_test_csv("pathway_test.csv", data)
        
        pathway_distribution = {
            "adenoma_carcinoma_ratio": 0.8,
            "serrated_adenoma_ratio": 0.2
        }
        
        population = self.generator.generate_population_from_structure_file(
            csv_file,
            pathway_distribution=pathway_distribution,
            show_progress=False
        )
        
        # 验证疾病通路分布
        adenoma_count = sum(
            1 for ind in population.individuals.values()
            if ind.pathway_type == PathwayType.ADENOMA_CARCINOMA
        )
        serrated_count = sum(
            1 for ind in population.individuals.values()
            if ind.pathway_type == PathwayType.SERRATED_ADENOMA
        )
        
        total_with_pathway = adenoma_count + serrated_count
        assert total_with_pathway == 20
        
        # 验证比例（允许一定误差）
        adenoma_ratio = adenoma_count / total_with_pathway
        assert 0.7 <= adenoma_ratio <= 0.9  # 期望0.8，允许误差
    
    def test_generation_summary(self):
        """测试生成摘要"""
        data = [
            {"age": 40, "gender": "male", "count": 5},
            {"age": 40, "gender": "female", "count": 5}
        ]
        
        csv_file = self.create_test_csv("summary_test.csv", data)
        
        population = self.generator.generate_population_from_structure_file(
            csv_file, show_progress=False
        )
        
        summary = self.generator.get_last_generation_summary()
        
        assert summary is not None
        assert summary.total_individuals == 10
        assert summary.generation_time > 0
        assert "source_file" in summary.config_used
        assert summary.config_used["total_structure_rows"] == 2
